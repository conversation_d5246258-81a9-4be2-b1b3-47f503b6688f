<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蔡总喊你打球啦！- 3D中文特效</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            color: #fff;
            touch-action: none; /* 防止移动端滚动 */
            user-select: none; /* 防止文字选择 */
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
            height: 100dvh; /* 动态视口高度，适配移动端 */
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            z-index: 100;
            font-size: 12px;
            line-height: 1.3;
            background: rgba(0,0,0,0.8);
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #00ffff;
            backdrop-filter: blur(10px);
            max-width: calc(100vw - 20px);
            box-sizing: border-box;
        }

        #info a {
            color: #00ffff;
            text-decoration: none;
        }

        #info a:hover, #info a:active {
            text-decoration: underline;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
            text-align: center;
            background: rgba(0,0,0,0.9);
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #00ffff;
            backdrop-filter: blur(10px);
            max-width: calc(100vw - 40px);
            box-sizing: border-box;
        }

        .loading-bar {
            width: min(250px, calc(100vw - 80px));
            height: 6px;
            background: #333;
            margin: 20px auto;
            border-radius: 3px;
            overflow: hidden;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #ff00ff);
            width: 0%;
            animation: loading 2s ease-in-out infinite;
        }

        @keyframes loading {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            z-index: 100;
            font-size: 11px;
            background: rgba(0,0,0,0.8);
            padding: 12px;
            border-radius: 8px;
            line-height: 1.3;
            border: 1px solid #00ffff;
            backdrop-filter: blur(10px);
            max-width: calc(100vw - 20px);
            box-sizing: border-box;
        }

        .highlight {
            color: #00ffff;
            font-weight: bold;
        }

        /* 移动端特定样式 */
        @media (max-width: 768px) {
            #info {
                font-size: 11px;
                padding: 10px;
                top: 5px;
                left: 5px;
                right: 5px;
            }

            #controls {
                font-size: 10px;
                padding: 10px;
                bottom: 5px;
                left: 5px;
                right: 5px;
            }

            #loading {
                font-size: 16px;
                padding: 20px;
                max-width: calc(100vw - 20px);
            }
        }

        /* 横屏模式优化 */
        @media (orientation: landscape) and (max-height: 500px) {
            #info {
                font-size: 10px;
                padding: 8px;
                line-height: 1.2;
            }

            #controls {
                font-size: 9px;
                padding: 8px;
                line-height: 1.2;
            }
        }

        /* 触摸反馈 */
        .touch-feedback {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 255, 255, 0.3);
            border: 2px solid #00ffff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 150;
        }

        .touch-feedback.active {
            opacity: 1;
            animation: touchPulse 0.6s ease-out;
        }

        @keyframes touchPulse {
            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <div>🎨 加载3D中文特效...</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div style="font-size: 14px; margin-top: 10px;">蔡总喊你打球啦！</div>
        </div>

        <div id="info">
            <strong class="highlight">3D中文文字特效</strong><br/>
            文字: <span class="highlight">"蔡总喊你打球啦！"</span><br/>
            <a href="https://threejs.org" target="_blank" rel="noopener">Three.js</a> ES模块版本
        </div>

        <div id="controls">
            <span class="highlight">控制说明:</span><br/>
            � 触摸拖拽: 旋转视角<br/>
            🤏 双指缩放: 放大缩小<br/>
            👆 双击: 自动旋转开关<br/>
            🔄 长按: 重置相机
        </div>

        <!-- 触摸反馈元素 -->
        <div class="touch-feedback" id="touchFeedback"></div>
    </div>

    <!-- 使用ES模块方式加载Three.js -->
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>
    
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

        let camera, scene, renderer, controls;
        let group, textMeshes = [];
        let isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        let touchFeedback;

        const text = "蔡总喊你打球啦！";

        // 根据设备类型调整参数
        const textParams = isMobile ? {
            size: 50,           // 移动端文字更小
            height: 12,         // 厚度减少
            spacing: 70         // 间距缩小
        } : {
            size: 75,
            height: 18,
            spacing: 110
        };

        let targetRotation = 0;
        let lastTouchTime = 0;
        let touchStartTime = 0;

        init();
        animate();

        function init() {
            console.log('🚀 初始化3D中文特效...');
            console.log('📱 设备类型:', isMobile ? '移动端' : '桌面端');

            // 场景 - 使用渐变背景色
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x1a1a2e);
            scene.fog = new THREE.Fog(0x1a1a2e, 500, 2000);

            // 相机 - 移动端优化
            const fov = isMobile ? 60 : 45; // 移动端使用更大的视野角
            camera = new THREE.PerspectiveCamera(fov, window.innerWidth / window.innerHeight, 1, 2000);

            // 移动端相机位置调整
            if (isMobile) {
                camera.position.set(0, 150, 500); // 更近的距离
            } else {
                camera.position.set(0, 200, 800);
            }

            // 渲染器 - 移动端性能优化
            renderer = new THREE.WebGLRenderer({
                antialias: !isMobile, // 移动端关闭抗锯齿以提升性能
                powerPreference: isMobile ? "low-power" : "high-performance"
            });

            // 移动端像素比限制
            const pixelRatio = isMobile ? Math.min(window.devicePixelRatio, 2) : window.devicePixelRatio;
            renderer.setPixelRatio(pixelRatio);
            renderer.setSize(window.innerWidth, window.innerHeight);

            // 移动端简化阴影设置
            if (!isMobile) {
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            }

            renderer.toneMapping = THREE.ACESFilmicToneMapping;
            renderer.toneMappingExposure = 1.2;
            document.getElementById('container').appendChild(renderer.domElement);

            // 控制器 - 移动端优化
            controls = new OrbitControls(camera, renderer.domElement);
            controls.target.set(0, 0, 0);
            controls.enableDamping = true;
            controls.dampingFactor = isMobile ? 0.1 : 0.05; // 移动端更快的阻尼
            controls.autoRotate = true;
            controls.autoRotateSpeed = isMobile ? 0.5 : 1; // 移动端更慢的自动旋转

            // 移动端触摸优化
            if (isMobile) {
                controls.enablePan = false; // 禁用平移，避免误操作
                controls.minDistance = 200;
                controls.maxDistance = 1000;
                controls.maxPolarAngle = Math.PI * 0.8; // 限制垂直旋转角度
            }

            // 获取触摸反馈元素
            touchFeedback = document.getElementById('touchFeedback');

            // 光照
            setupLights();

            // 创建文字组
            group = new THREE.Group();
            scene.add(group);

            // 创建中文文字
            createChineseTextMeshes();

            // 事件监听
            setupEventListeners();

            // 隐藏加载界面
            setTimeout(() => {
                const loading = document.getElementById('loading');
                if (loading) loading.style.display = 'none';
                console.log('✅ 3D中文特效加载完成！');
            }, 1500);
        }

        function setupLights() {
            // 环境光 - 增强亮度
            const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
            scene.add(ambientLight);

            // 主方向光 - 白色强光
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5);
            directionalLight.position.set(100, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 彩色点光源1 - 青色
            const pointLight1 = new THREE.PointLight(0x00ffff, 2, 1000);
            pointLight1.position.set(-200, 100, 200);
            scene.add(pointLight1);

            // 彩色点光源2 - 洋红色
            const pointLight2 = new THREE.PointLight(0xff00ff, 2, 1000);
            pointLight2.position.set(200, 100, 200);
            scene.add(pointLight2);

            // 彩色点光源3 - 黄色
            const pointLight3 = new THREE.PointLight(0xffff00, 1.5, 1000);
            pointLight3.position.set(0, -100, 100);
            scene.add(pointLight3);
        }

        function createChineseTextMeshes() {
            console.log('🎨 创建中文3D文字...');
            const characters = ['蔡', '总', '喊', '你', '打', '球', '啦', '！'];
            const startX = -(characters.length - 1) * textParams.spacing / 2;

            characters.forEach((char, index) => {
                const mesh = createChineseCharacterMesh(char, index);
                mesh.position.x = startX + index * textParams.spacing;
                mesh.position.y = 0;
                mesh.position.z = 0;
                
                group.add(mesh);
                textMeshes.push(mesh);
                
                console.log(`✨ 创建文字: ${char}`);
            });
        }

        function createChineseCharacterMesh(character, index) {
            // 创建立方体几何体 - 更大更明显
            const geometry = new THREE.BoxGeometry(
                textParams.size, 
                textParams.size, 
                textParams.height
            );

            // 创建高对比度文字纹理
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 512;
            canvas.height = 512;

            // 深色背景
            context.fillStyle = '#000000';
            context.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制白色文字
            context.fillStyle = '#ffffff';
            context.font = 'bold 300px "Microsoft YaHei", "SimHei", "黑体", Arial, sans-serif';
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            
            // 添加发光效果
            context.shadowColor = '#00ffff';
            context.shadowBlur = 30;
            context.fillText(character, canvas.width/2, canvas.height/2);
            
            // 再次绘制以增强效果
            context.shadowBlur = 0;
            context.fillStyle = '#ffffff';
            context.fillText(character, canvas.width/2, canvas.height/2);

            const texture = new THREE.CanvasTexture(canvas);
            texture.generateMipmaps = false;
            texture.minFilter = THREE.LinearFilter;

            // 创建发光材质
            const material = new THREE.MeshPhongMaterial({ 
                map: texture,
                color: 0xffffff,
                emissive: 0x222222,
                shininess: 100,
                transparent: false
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;

            return mesh;
        }

        function setupEventListeners() {
            // 键盘事件（桌面端）
            if (!isMobile) {
                document.addEventListener('keydown', onDocumentKeyDown, false);
            }

            // 移动端触摸事件
            if (isMobile) {
                setupMobileEvents();
            }

            // 窗口大小调整
            window.addEventListener('resize', onWindowResize, false);

            // 防止移动端页面滚动
            document.addEventListener('touchmove', function(e) {
                e.preventDefault();
            }, { passive: false });
        }

        function setupMobileEvents() {
            const canvas = renderer.domElement;

            // 触摸开始
            canvas.addEventListener('touchstart', onTouchStart, { passive: false });

            // 双击事件 - 切换自动旋转
            let tapCount = 0;
            canvas.addEventListener('touchend', function(e) {
                e.preventDefault();
                const currentTime = new Date().getTime();
                const tapLength = currentTime - lastTouchTime;

                if (tapLength < 500 && tapLength > 0) {
                    tapCount++;
                    if (tapCount === 2) {
                        // 双击
                        controls.autoRotate = !controls.autoRotate;
                        showTouchFeedback(e.changedTouches[0]);
                        console.log('🔄 自动旋转:', controls.autoRotate ? '开启' : '关闭');
                        tapCount = 0;
                    }
                } else {
                    tapCount = 1;
                }
                lastTouchTime = currentTime;

                // 长按检测结束
                if (touchStartTime > 0) {
                    const touchDuration = currentTime - touchStartTime;
                    if (touchDuration > 1000) { // 长按超过1秒
                        resetCamera();
                        showTouchFeedback(e.changedTouches[0]);
                        console.log('🎯 相机重置');
                    }
                    touchStartTime = 0;
                }
            }, { passive: false });

            // 清除单击计数器
            setTimeout(() => {
                tapCount = 0;
            }, 500);
        }

        function onTouchStart(e) {
            e.preventDefault();
            touchStartTime = new Date().getTime();

            // 显示触摸反馈
            if (e.touches.length === 1) {
                showTouchFeedback(e.touches[0]);
            }
        }

        function showTouchFeedback(touch) {
            if (!touchFeedback) return;

            const rect = renderer.domElement.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;

            touchFeedback.style.left = x + 'px';
            touchFeedback.style.top = y + 'px';
            touchFeedback.classList.add('active');

            setTimeout(() => {
                touchFeedback.classList.remove('active');
            }, 600);
        }

        function resetCamera() {
            if (isMobile) {
                camera.position.set(0, 150, 500);
            } else {
                camera.position.set(0, 200, 800);
            }
            controls.target.set(0, 0, 0);
            controls.update();
        }

        function onDocumentKeyDown(event) {
            switch(event.code) {
                case 'Space':
                    event.preventDefault();
                    controls.autoRotate = !controls.autoRotate;
                    console.log('🔄 自动旋转:', controls.autoRotate ? '开启' : '关闭');
                    break;
                case 'KeyR':
                    resetCamera();
                    console.log('🎯 相机重置');
                    break;
            }
        }

        function onWindowResize() {
            // 更新相机宽高比
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();

            // 更新渲染器大小
            renderer.setSize(window.innerWidth, window.innerHeight);

            // 移动端重新检测设备方向
            if (isMobile) {
                const isLandscape = window.innerWidth > window.innerHeight;
                console.log('📱 设备方向:', isLandscape ? '横屏' : '竖屏');

                // 根据方向调整相机位置
                if (isLandscape) {
                    camera.position.set(0, 100, 400);
                } else {
                    camera.position.set(0, 150, 500);
                }
                controls.update();
            }
        }

        function animate() {
            requestAnimationFrame(animate);

            const time = Date.now() * 0.001;

            // 文字动画 - 移动端性能优化
            textMeshes.forEach((mesh, index) => {
                const offset = index * 0.5;

                // 浮动效果 - 移动端减少幅度
                const floatAmplitude = isMobile ? 20 : 30;
                mesh.position.y = Math.sin(time * 1.5 + offset) * floatAmplitude;

                // 独立旋转 - 移动端减少旋转幅度
                const rotationAmplitude = isMobile ? 0.1 : 0.2;
                mesh.rotation.x = Math.sin(time * 1.2 + offset) * rotationAmplitude;
                mesh.rotation.z = Math.cos(time * 0.8 + offset) * (rotationAmplitude * 0.5);

                // 彩虹色彩变化 - 移动端降低变化频率
                const colorSpeed = isMobile ? 0.2 : 0.3;
                const hue = (time * colorSpeed + index * 0.15) % 1;
                mesh.material.color.setHSL(hue, 0.8, 0.9);

                // 发光效果变化 - 移动端简化
                if (!isMobile) {
                    const emissiveIntensity = 0.1 + Math.sin(time * 2 + offset) * 0.1;
                    mesh.material.emissive.setHSL(hue, 0.5, emissiveIntensity);
                } else {
                    // 移动端使用固定发光强度以提升性能
                    mesh.material.emissive.setHSL(hue, 0.3, 0.05);
                }
            });

            // 更新控制器
            controls.update();

            // 渲染
            renderer.render(scene, camera);
        }
    </script>
</body>
</html>
